@inject IJSRuntime JSRuntime
@inject ISnackbar Snackbar
@inject IHttpClientFactory HttpClientFactory

<div class="text-file-preview">
    @if (loading)
    {
        <div class="d-flex justify-center align-center" style="height: 200px;">
            <MudProgressCircular Indeterminate="true" Size="Size.Medium" />
            <MudText Class="ml-3">正在加载文件内容...</MudText>
        </div>
    }
    else if (error != null)
    {
        <MudAlert Severity="Severity.Error">
            <div>
                <MudText Typo="Typo.body1" Class="font-weight-medium">加载失败</MudText>
                <MudText Typo="Typo.body2">@error</MudText>
            </div>
        </MudAlert>
    }
    else if (fileContent != null)
    {
        @if (fileContent.Type == "csv")
        {
            <!-- CSV 表格预览 -->
            <div class="mb-3">
                <MudText Typo="Typo.h6" Class="mb-2">
                    <MudIcon Icon="@Icons.Material.Filled.TableChart" Class="mr-2" />
                    CSV 文件预览
                </MudText>
                @if (fileContent.IsLimited)
                {
                    <MudAlert Severity="Severity.Info" Dense="true" Class="mb-2">
                        显示前 100 行，总共 @fileContent.TotalLines 行
                    </MudAlert>
                }
            </div>
            
            <MudDataGrid T="string[]" Items="@fileContent.CsvData" 
                         Hover="true" Dense="true" Bordered="true"
                         Style="max-height: 400px; overflow-y: auto;">
                <Columns>
                    @if (fileContent.CsvData?.Any() == true)
                    {
                        @for (int i = 0; i < fileContent.CsvData.First().Length; i++)
                        {
                            var columnIndex = i; // 闭包变量
                            <PropertyColumn Property="x => x.Length > columnIndex ? x[columnIndex] : string.Empty" 
                                          Title="@($"列 {columnIndex + 1}")" />
                        }
                    }
                </Columns>
            </MudDataGrid>
        }
        else
        {
            <!-- 普通文本预览 -->
            <div class="mb-3">
                <MudText Typo="Typo.h6" Class="mb-2">
                    <MudIcon Icon="@Icons.Material.Filled.TextSnippet" Class="mr-2" />
                    文本文件预览
                </MudText>
                <MudButtonGroup Variant="Variant.Outlined" Size="Size.Small">
                    <MudButton StartIcon="@Icons.Material.Filled.WrapText" 
                             OnClick="@(() => wordWrap = !wordWrap)"
                             Color="@(wordWrap ? Color.Primary : Color.Default)">
                        自动换行
                    </MudButton>
                    <MudButton StartIcon="@Icons.Material.Filled.ContentCopy" 
                             OnClick="CopyToClipboard">
                        复制内容
                    </MudButton>
                </MudButtonGroup>
            </div>
            
            <MudPaper Class="pa-3" Style="max-height: 400px; overflow-y: auto; background-color: #f5f5f5;">
                <pre style="@($"white-space: {(wordWrap ? "pre-wrap" : "pre")}; font-family: 'Courier New', monospace; font-size: 12px; margin: 0; line-height: 1.4;")">@fileContent.Content</pre>
            </MudPaper>
        }
    }
</div>

@code {
    [Parameter] public string FileName { get; set; } = string.Empty;
    [Parameter] public string FileExtension { get; set; } = string.Empty;

    private bool loading = true;
    private string? error = null;
    private FileContentModel? fileContent = null;
    private bool wordWrap = true;

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(FileName))
        {
            await LoadFileContent();
        }
    }

    private async Task LoadFileContent()
    {
        try
        {
            loading = true;
            error = null;
            StateHasChanged();

            using var httpClient = HttpClientFactory.CreateClient();
            var response = await httpClient.GetAsync($"/api/ExamFile/preview-text/{FileName}");
            
            if (response.IsSuccessStatusCode)
            {
                var jsonContent = await response.Content.ReadAsStringAsync();
                fileContent = System.Text.Json.JsonSerializer.Deserialize<FileContentModel>(jsonContent, new System.Text.Json.JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            else
            {
                error = $"加载失败: {response.StatusCode}";
            }
        }
        catch (Exception ex)
        {
            error = $"加载失败: {ex.Message}";
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }

    private async Task CopyToClipboard()
    {
        try
        {
            if (fileContent?.Content != null)
            {
                await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", fileContent.Content);
                Snackbar.Add("内容已复制到剪贴板", Severity.Success);
            }
        }
        catch (Exception)
        {
            Snackbar.Add("复制失败，请手动选择复制", Severity.Warning);
        }
    }

    public class FileContentModel
    {
        public string Type { get; set; } = string.Empty;
        public string? Content { get; set; }
        public string? FileName { get; set; }
        public List<string[]>? CsvData { get; set; }
        public int TotalLines { get; set; }
        public bool IsLimited { get; set; }
    }
}

<style>
    .text-file-preview {
        width: 100%;
        height: 100%;
    }
    
    .text-file-preview pre {
        border: none;
        background: transparent;
    }
    
    .text-file-preview .mud-data-grid {
        font-size: 12px;
    }
    
    .text-file-preview .mud-data-grid .mud-table-cell {
        padding: 4px 8px;
    }
</style>
