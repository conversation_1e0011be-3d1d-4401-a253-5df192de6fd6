@page "/Exam/Take/{ExamId:int}"
@using SqlSugar
@using SuntechApp.Data
@inject ISqlSugarClient db
@inject ISnackbar Snackbar
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer

<PageTitle>参加考试</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    @if (loading)
    {
        <MudProgressCircular Indeterminate="true" />
        <MudText Class="ml-2">加载中...</MudText>
    }
    else if (examPaper == null)
    {
        <MudAlert Severity="Severity.Error">
            <MudText>考试不存在或已关闭</MudText>
        </MudAlert>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" 
                   OnClick="@(() => Navigation.NavigateTo("/Exam"))"
                   Class="mt-3">
                                                                                                                            返回考试列表
                                                                                                                            </MudButton>
    }
    else
    {
        <MudText Typo="Typo.h4" GutterBottom="true" Class="mb-6">@examPaper.Title</MudText>
        
        <MudGrid>
            <!-- 试卷下载区域 -->
            <MudItem xs="12" md="6">
                <MudPaper Class="pa-4" Elevation="2">
                    <MudText Typo="Typo.h6" Class="mb-4">
                        <MudIcon Icon="@Icons.Material.Filled.Download" Class="mr-2" />
                        下载试卷
                    </MudText>
                    
                    <MudText Typo="Typo.body1" Class="mb-3">
                        文件名：@examPaper.FileName
                    </MudText>
                    <MudText Typo="Typo.body2" Color="Color.Info" Class="mb-4">
                        上传时间：@examPaper.UploadedDate.ToString("yyyy-MM-dd HH:mm")
                    </MudText>
                    
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="@Icons.Material.Filled.Download"
                             OnClick="@DownloadPaper">
                        下载试卷文件
                    </MudButton>
                </MudPaper>
            </MudItem>

            <!-- 答案提交区域 -->
            <MudItem xs="12" md="6">
                <MudPaper Class="pa-4" Elevation="2">
                    <MudText Typo="Typo.h6" Class="mb-4">
                        <MudIcon Icon="@Icons.Material.Filled.Upload" Class="mr-2" />
                        提交答案
                    </MudText>
                    
                    @if (hasSubmitted && existingSubmission != null)
                    {
                        <MudAlert Severity="Severity.Info" Class="mb-4">
                            <div>
                                <MudText Typo="Typo.h6" Class="mb-2">您已提交过答案</MudText>
                                <MudText Typo="Typo.body2">学生：@existingSubmission.StudentName (@existingSubmission.StudentNumber)</MudText>
                                <MudText Typo="Typo.body2">提交时间：@existingSubmission.SubmittedDate.ToString("yyyy-MM-dd HH:mm")</MudText>
                                <MudText Typo="Typo.body2">答案文件：@(existingSubmissionFiles?.Count ?? 0) 个文件</MudText>
                                @if (existingSubmissionFiles?.Any() == true)
                                {
                                    <div class="ml-4 mt-2">
                                        @foreach (var file in existingSubmissionFiles.Take(3))
                                        {
                                            <div class="d-flex align-center mb-1">
                                                <MudIcon Icon="@GetFileIcon(Path.GetExtension(file.FileName))" Size="Size.Small" Class="mr-2" />
                                                <MudText Typo="Typo.caption">@file.FileName</MudText>
                                            </div>
                                        }
                                        @if (existingSubmissionFiles.Count > 3)
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">... 还有 @(existingSubmissionFiles.Count - 3) 个文件</MudText>
                                        }
                                    </div>
                                }
                                @if (existingSubmission.Score.HasValue)
                                {
                                    <MudText Typo="Typo.body2" Class="mt-2">
                                        <MudChip T="string" Color="Color.Success" Size="Size.Small">已评分：@existingSubmission.Score.Value.ToString("F1")分</MudChip>
                                    </MudText>
                                    @if (!string.IsNullOrEmpty(existingSubmission.Comments))
                                    {
                                        <MudText Typo="Typo.body2" Class="mt-1">评语：@existingSubmission.Comments</MudText>
                                    }
                                }
                                else
                                {
                                    <MudText Typo="Typo.body2" Class="mt-2">
                                        <MudChip T="string" Color="Color.Default" Size="Size.Small">等待评分</MudChip>
                                    </MudText>
                                }
                            </div>
                        </MudAlert>
                    }
                    else
                    {
                        <MudAlert Severity="Severity.Info" Class="mb-4" Dense="true">
                            <div class="d-flex align-center">
                                <MudIcon Icon="@Icons.Material.Filled.Lightbulb" Class="mr-2" />
                                <div>
                                    <MudText Typo="Typo.body2" Class="font-weight-medium">温馨提示</MudText>
                                    <MudText Typo="Typo.caption">
                                        选择文件时，浏览器可能会短暂冻结，这是正常现象。请耐心等待文件选择器打开。
                                    </MudText>
                                </div>
                            </div>
                        </MudAlert>

                        <MudForm @ref="form" @bind-IsValid="@isFormValid">
                            <MudTextField @bind-Value="studentName"
                                        Label="姓名"
                                        Required="true"
                                        RequiredError="请输入姓名"
                                        Class="mb-3" />

                            <MudTextField @bind-Value="studentNumber"
                                        Label="工号"
                                        Required="true"
                                        RequiredError="请输入工号"
                                        Class="mb-3"
                                        OnBlur="@CheckExistingSubmission" />
                        
                        <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                                     Accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.csv,.txt"
                                     FilesChanged="@OnFilesSelected"
                                     MaximumFileCount="10">
                            <ActivatorContent>
                                <MudButton Variant="Variant.Outlined"
                                         Color="Color.Primary"
                                         StartIcon="@(isSelectingFile ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.CloudUpload)"
                                         OnClick="@OnFileSelectClick"
                                         Disabled="@isSelectingFile">
                                    @if (isSelectingFile)
                                    {
                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                        <span>正在上传文件...</span>
                                    }
                                    else
                                    {
                                        <span>选择答案文件（可选择多个）</span>
                                    }
                                </MudButton>
                            </ActivatorContent>
                        </MudFileUpload>

                        @if (isSelectingFile)
                        {
                            <MudAlert Severity="Severity.Info" Class="mt-3" Dense="true">
                                        <MudText Typo="Typo.body2">
                                            正在打开文件选择器，浏览器可能会短暂无响应，这是正常现象，请耐心等待...
                                        </MudText>
                            </MudAlert>
                        }

                        @if (selectedFiles?.Any() == true)
                        {
                            <MudAlert Severity="Severity.Success" Class="mt-3" Dense="true">
                                <MudText Typo="Typo.body2" Class="font-weight-medium mb-2">
                                    已选择 @selectedFiles.Count 个文件：
                                </MudText>
                                @foreach (var file in selectedFiles)
                                {
                                    <div class="d-flex align-center mb-1">
                                        <MudIcon Icon="@GetFileIcon(Path.GetExtension(file.Name))" Size="Size.Small" Class="mr-2" />
                                        <MudText Typo="Typo.caption" Class="mr-2">@file.Name</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">(@FormatFileSize(file.Size))</MudText>
                                    </div>
                                }
                                <MudText Typo="Typo.caption" Color="Color.Info" Class="mt-2">
                                    文件选择成功，现在可以提交答案了
                                </MudText>
                            </MudAlert>
                        }

                        @if (submitting)
                        {
                            <MudCard Class="mt-3 pa-4" Style="background-color: #f5f5f5;">
                                <div class="d-flex align-center mb-3">
                                    <MudIcon Icon="@Icons.Material.Filled.Commit"
                                            Color="Color.Success"
                                            Class="mr-2 rotating" />
                                    <MudText Typo="Typo.h6">正在提交答案...</MudText>
                                </div>
                                <MudProgressLinear Value="@submitProgress"
                                                 Color="Color.Success"
                                                 Size="Size.Medium"
                                                 Class="mb-2" />
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2">@($"{submitProgress:F0}%")</MudText>
                                    <MudText Typo="Typo.body2">@submitStatusText</MudText>
                                </div>
                            </MudCard>
                        }
                        
                            <MudButton Variant="Variant.Filled"
                                     Color="Color.Success"
                                     OnClick="@SubmitAnswer"
                                     Disabled="@(!isFormValid || !selectedFiles.Any() || submitting || hasSubmitted)"
                                     Class="mt-3">
                                @if (submitting)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                    <span class="ml-2">提交中...</span>
                                }
                                else
                                {
                                    <span>提交答案</span>
                                }
                            </MudButton>
                        </MudForm>
                    }
                </MudPaper>
            </MudItem>
        </MudGrid>
    }
</MudContainer>

@code {
    [Parameter] public int ExamId { get; set; }
    
    private SqlSugarProvider DefaultDb => db.AsTenant().GetConnection("Default");
    private ExamPaper? examPaper;
    private bool loading = true;
    private bool submitting = false;
    private bool isFormValid = false;
    private bool hasSubmitted = false;
    private ExamSubmission? existingSubmission;
    private List<ExamSubmissionFile>? existingSubmissionFiles;

    private string studentName = string.Empty;
    private string studentNumber = string.Empty;
    private List<IBrowserFile> selectedFiles = new();
    private MudForm form = null!;
    private bool isSelectingFile = false;

    // 文件上传限制
    private const int MaxFileCount = 10;
    private const long MaxTotalSize = 100 * 1024 * 1024; // 100MB
    private const long MaxSingleFileSize = 50 * 1024 * 1024; // 50MB

    // 进度条相关变量
    private double submitProgress = 0;
    private string submitStatusText = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadExamPaper();
    }

    private async Task LoadExamPaper()
    {
        try
        {
            loading = true;
            StateHasChanged();

            examPaper = await DefaultDb.Queryable<ExamPaper>()
                .Where(x => x.Id == ExamId && x.IsActive)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载考试信息失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }

    private async Task DownloadPaper()
    {
        if (examPaper == null) return;

        try
        {
            var url = $"/api/ExamFile/download-paper/{Path.GetFileName(examPaper.FilePath)}";
            await JSRuntime.InvokeVoidAsync("open", url, "_blank");
            Snackbar.Add("下载已开始，请查看浏览器下载内容", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"下载失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task OnFileSelectClick()
    {
        isSelectingFile = true;
        StateHasChanged();

        // 给UI一点时间更新
        await Task.Delay(100);
    }

    private async Task OnFilesSelected(IReadOnlyList<IBrowserFile> files)
    {
        // 文件选择完成，重置状态
        isSelectingFile = false;

        try
        {
            selectedFiles.Clear();

            if (files?.Any() == true)
            {
                // 检查文件数量限制
                if (files.Count > MaxFileCount)
                {
                    Snackbar.Add($"最多只能选择 {MaxFileCount} 个文件", Severity.Error);
                    StateHasChanged();
                    return;
                }

                // 检查单个文件大小和总大小
                var totalSize = 0L;
                var validFiles = new List<IBrowserFile>();

                foreach (var file in files)
                {
                    if (file.Size > MaxSingleFileSize)
                    {
                        Snackbar.Add($"文件 {file.Name} 大小超过 50MB 限制", Severity.Error);
                        continue;
                    }

                    totalSize += file.Size;
                    validFiles.Add(file);
                }

                if (totalSize > MaxTotalSize)
                {
                    Snackbar.Add($"文件总大小超过 100MB 限制", Severity.Error);
                    StateHasChanged();
                    return;
                }

                selectedFiles.AddRange(validFiles);

                if (selectedFiles.Any())
                {
                    await Task.Delay(500);
                    Snackbar.Add($"成功选择 {selectedFiles.Count} 个文件", Severity.Success);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"文件选择失败: {ex.Message}", Severity.Error);
        }

        StateHasChanged();
    }

    private string GetFileIcon(string? extension)
    {
        return extension?.ToLower() switch
        {
            ".pdf" => Icons.Material.Filled.PictureAsPdf,
            ".docx" or ".doc" => Icons.Material.Filled.Description,
            ".xlsx" or ".xls" => Icons.Material.Filled.TableChart,
            ".pptx" or ".ppt" => Icons.Material.Filled.Slideshow,
            ".png" or ".jpg" or ".jpeg" or ".gif" or ".bmp" => Icons.Material.Filled.Image,
            ".zip" or ".rar" or ".7z" => Icons.Material.Filled.Archive,
            ".txt" => Icons.Material.Filled.TextSnippet,
            ".csv" => Icons.Material.Filled.TableChart,
            _ => Icons.Material.Filled.AttachFile
        };
    }

    private async Task CheckExistingSubmission()
    {
        if (string.IsNullOrEmpty(studentNumber) || examPaper == null) return;

        try
        {
            existingSubmission = await DefaultDb.Queryable<ExamSubmission>()
                .Where(x => x.ExamPaperId == ExamId && x.StudentNumber == studentNumber)
                .FirstAsync();

            hasSubmitted = existingSubmission != null;
            StateHasChanged();
        }
        catch (Exception)
        {
            // 没有找到提交记录，这是正常的
            hasSubmitted = false;
            existingSubmission = null;
        }
    }

    private async Task SubmitAnswer()
    {
        if (examPaper == null || !selectedFiles.Any()) return;

        try
        {
            submitting = true;
            submitProgress = 0;
            submitStatusText = "开始提交...";
            StateHasChanged();

            // 检查是否已经提交过答案
            submitProgress = 10;
            submitStatusText = "检查提交状态...";
            StateHasChanged();
            await Task.Delay(200);

            if (hasSubmitted)
            {
                Snackbar.Add($"工号 {studentNumber} 已经提交过此考试的答案，不能重复提交！", Severity.Warning);
                return;
            }

            // 验证文件
            submitProgress = 20;
            submitStatusText = "验证文件...";
            StateHasChanged();
            await Task.Delay(200);

            var allowedTypes = new[] { ".pdf", ".doc", ".docx", ".ppt", ".pptx", ".xls", ".xlsx", ".csv", ".txt" };
            var totalSize = selectedFiles.Sum(f => f.Size);

            // 验证总大小
            if (totalSize > MaxTotalSize)
            {
                Snackbar.Add("文件总大小不能超过100MB", Severity.Error);
                return;
            }

            // 验证每个文件
            foreach (var file in selectedFiles)
            {
                if (file.Size > MaxSingleFileSize)
                {
                    Snackbar.Add($"文件 {file.Name} 大小不能超过50MB", Severity.Error);
                    return;
                }

                var fileExtension = Path.GetExtension(file.Name).ToLower();
                if (!allowedTypes.Contains(fileExtension))
                {
                    Snackbar.Add($"不支持的文件类型: {file.Name}", Severity.Error);
                    return;
                }
            }

            // 创建保存目录
            submitProgress = 40;
            submitStatusText = "创建保存目录...";
            StateHasChanged();
            await Task.Delay(200);

            var submissionPath = @"C:\ExamFiles\Submissions";
            if (!Directory.Exists(submissionPath))
            {
                Directory.CreateDirectory(submissionPath);
            }

            // 先创建提交记录
            submitProgress = 50;
            submitStatusText = "创建提交记录...";
            StateHasChanged();
            await Task.Delay(200);

            var submission = new ExamSubmission
            {
                ExamPaperId = ExamId,
                StudentName = studentName,
                StudentNumber = studentNumber,
                SubmittedDate = DateTime.Now
            };

            // 插入提交记录并获取ID
            var submissionId = await DefaultDb.Insertable(submission).ExecuteReturnIdentityAsync();

            // 保存文件并创建文件记录
            var savedFiles = new List<ExamSubmissionFile>();
            var fileIndex = 0;
            var totalFiles = selectedFiles.Count;

            foreach (var file in selectedFiles)
            {
                fileIndex++;

                // 更新进度
                submitProgress = 50 + (fileIndex * 35 / totalFiles); // 50% - 85%
                submitStatusText = $"保存文件 {fileIndex}/{totalFiles}: {file.Name}";
                StateHasChanged();

                // 生成唯一文件名
                var fileExtension = Path.GetExtension(file.Name);
                var fileName = $"{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(submissionPath, fileName);

                // 保存文件
                using var stream = new FileStream(filePath, FileMode.Create);
                var fileStream = file.OpenReadStream(MaxSingleFileSize);
                await fileStream.CopyToAsync(stream);

                // 创建文件记录
                var submissionFile = new ExamSubmissionFile
                {
                    SubmissionId = submissionId,
                    FileName = file.Name,
                    FilePath = filePath,
                    UploadedDate = DateTime.Now,
                    FileSize = file.Size,
                    FileType = fileExtension.TrimStart('.'),
                    DisplayOrder = fileIndex
                };

                savedFiles.Add(submissionFile);
                await Task.Delay(100); // 让用户看到进度
            }

            // 批量插入文件记录
            submitProgress = 85;
            submitStatusText = "保存文件信息...";
            StateHasChanged();
            await Task.Delay(200);

            if (savedFiles.Any())
            {
                await DefaultDb.Insertable(savedFiles).ExecuteCommandAsync();
            }

            // 完成提交

            submitProgress = 100;
            submitStatusText = "提交完成！";
            StateHasChanged();
            await Task.Delay(500);

            Snackbar.Add($"成功提交 {selectedFiles.Count} 个文件！", Severity.Success);

            // 延迟跳转
            await Task.Delay(2000);
            Navigation.NavigateTo("/Exam");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"提交失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            submitting = false;
            submitProgress = 0;
            submitStatusText = "";
            StateHasChanged();
        }
    }

    private string FormatFileSize(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
        return $"{bytes / (1024 * 1024):F1} MB";
    }
}

<style>
    .rotating {
        animation: rotate 2s linear infinite;
    }

    @@keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>
