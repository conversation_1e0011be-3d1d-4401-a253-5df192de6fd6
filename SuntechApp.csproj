<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-SuntechApp-af3eeaa4-1649-4157-9091-37b9ed6fcc18</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="Components\Pages\Card.razor" />
    <Content Remove="Components\Pages\Component.razor" />
    <Content Remove="Components\Pages\Footer.razor" />
    <Content Remove="Components\Dialogs\StudentFilePreviewDialog.razor" />
    <Content Remove="Components\Pages\AnswerPreviewDialog.razor" />
  </ItemGroup>


  <ItemGroup>
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.20" />
    <PackageReference Include="Hangfire.Core" Version="1.8.20" />
    <PackageReference Include="Hangfire.SqlServer" Version="1.8.20" />
    <PackageReference Include="HtmlAgilityPack" Version="1.12.2" />
    <PackageReference Include="MailKit" Version="4.13.0" />
    <PackageReference Include="Markdig" Version="0.41.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.Extensions" Version="6.0.36" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="MiniExcel" Version="1.41.3" />
    <PackageReference Include="MudBlazor" Version="8.10.0" />
    <PackageReference Include="Extensions.MudBlazor.StaticInput" Version="3.2.1" />
    <PackageReference Include="MudBlazor.Markdown" Version="8.9.0" />
    <PackageReference Include="MudBlazor.Translations" Version="2.5.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PdfSharpCore" Version="1.3.67" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.10" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.198" />
  </ItemGroup>


  <ItemGroup>
    <Folder Include="Components\Dialogs\" />
    <Folder Include="Components\Shared\" />
    <Folder Include="Models\" />
    <Folder Include="wwwroot\images\" />
    <Folder Include="wwwroot\tmp\" />
  </ItemGroup>

</Project>