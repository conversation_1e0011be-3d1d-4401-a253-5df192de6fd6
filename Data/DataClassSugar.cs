using SqlSugar;

namespace SuntechApp.Data
{
    [TenantAttribute("BPMDATA")]
    [SugarTable("Parameter_Nextkeys")]
    public class BpmNextkeys
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public string? KeyPrefix { get; set; }
        public string? SubKey { get; set; }
        public int? KeyID { get; set; }
    }

    [TenantAttribute("BPMDATA")]
    [SugarTable("IT_internal_website")]
    public class BpmInternalWebsite
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public int TaskID { get; set; }
        [SugarColumn(ColumnName = "form_num")]
        public string? FormNum { get; set; }
        [SugarColumn(ColumnName = "applicant")]
        public string? Applicant { get; set; }
        public string? Type { get; set; }
        public string? Title { get; set; }
        public string? Content { get; set; }
    }
    [TenantAttribute("BPMDATA")]
    [SugarTable("IT_internal_website_upload_train")]
    public class BpmInternalWebsiteTrain
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public int TaskID { get; set; }
        public int? TmId { get; set; }
        public string? Title { get; set; }
        public int ParentId { get; set; }
        public string? Attach { get; set; }
        public string? Slug { get; set; }
    }

    [TenantAttribute("Default")]
    [SugarTable("News")]
    public class News
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int NewsID { get; set; }
        public string? Title { get; set; }
        public string? Content { get; set; }
        public DateTime? PublishedDate { get; set; }
        public int? CategoryID { get; set; }
        public string? Author { get; set; }
        public string? Slug { get; set; }
    }
    
    [TenantAttribute("Default")]
    public class TrainingMaterialClass
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public string Class { get; set; }
        public int ParentId { get; set; }
        public bool HaveNested { get; set; }
    }
    
    [TenantAttribute("Default")]
    [SugarTable("TrainingMaterial")]
    public class TrainingMaterial
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(Length = 60, IsNullable = false)]
        public string Title { get; set; } = default!;

        [SugarColumn(IsNullable = false)]
        public int ParentId { get; set; }

        [SugarColumn(Length = 25, IsNullable = true)]
        public string Author { get; set; } = default!;

        [SugarColumn(Length = 25, IsNullable = true)]
        public string Slug { get; set; } = default!;

        public DateTime? CreatedDate { get; set; }

        public DateTime? ModifiedDate { get; set; }

        [SugarColumn(Length = 50, IsNullable = false)]
        public string? FileID { get; set; }
        [SugarColumn(Length = 50, IsNullable = false)]
        public string? BpmFileID { get; set; }
    }
    [TenantAttribute("BPMDATA")]
    [SugarTable("bpmdbAttachment")]
    public class YZAppAttachment
    {
        [SugarColumn(IsPrimaryKey = true, Length = 50, IsNullable = false)]
        public string FileID { get; set; } = default!;

        [SugarColumn(Length = 256, IsNullable = true)]
        public string? Name { get; set; }

        [SugarColumn(Length = 8, IsNullable = true)]
        public string? Ext { get; set; }

        [SugarColumn(IsNullable = true)]
        public int? Size { get; set; }

        [SugarColumn(IsNullable = true)]
        public DateTime? LastUpdate { get; set; }

        [SugarColumn(Length = 30, IsNullable = true)]
        public string? OwnerAccount { get; set; }

        [SugarColumn(IsNullable = true)]
        public int? LParam1 { get; set; }
    }
    [TenantAttribute("BPMDATA")]
    [SugarTable("RD_Work_Log_Registration_Detail")]
    public class RDWorkLogRegistrationDetail
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "TaskID")]
        public int? TaskID { get; set; }

        [SugarColumn(ColumnName = "acct_unit3", Length = 4)]
        public string AcctUnit3 { get; set; }

        [SugarColumn(ColumnName = "descrip_unit3", Length = 40)]
        public string DescriptionUnit3 { get; set; }

        [SugarColumn(ColumnName = "start_date")]
        public DateTime? StartDate { get; set; }

        [SugarColumn(ColumnName = "end_date")]
        public DateTime? EndDate { get; set; }

        [SugarColumn(ColumnName = "wh1", DecimalDigits = 2)]
        public decimal? WorkHours { get; set; }

        [SugarColumn(ColumnName = "work_content", Length = 50)]
        public string WorkContent { get; set; }

        [SugarColumn(ColumnName = "projects_person", Length = 15)]
        public string ProjectsPerson { get; set; }

        [SugarColumn(ColumnName = "rd_director", Length = 15)]
        public string RdDirector { get; set; }
    }
    
    [TenantAttribute("BPMDATA")]
    [SugarTable("RD_Work_Log_Registration_Detail_Class")]
    public class RDWorkLogRegistrationDetailClass
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "TaskID")]
        public int? TaskID { get; set; }

        [SugarColumn(ColumnName = "acct_unit3", Length = 4)]
        public string AcctUnit3 { get; set; }

        [SugarColumn(ColumnName = "descrip_unit3", Length = 40)]
        public string DescriptionUnit3 { get; set; }

        [SugarColumn(ColumnName = "wh2", DecimalDigits = 2)]
        //合计工时
        public decimal? WorkHoursTotal { get; set; }
    }
    [TenantAttribute("Default")]
    [SugarTable("ExamPapers")]
    public class ExamPaper
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(Length = 200, IsNullable = false)]
        public string Title { get; set; } = string.Empty;

        [SugarColumn(Length = 255, IsNullable = false)]
        public string FileName { get; set; } = string.Empty;

        [SugarColumn(Length = 500, IsNullable = false)]
        public string FilePath { get; set; } = string.Empty;

        public DateTime UploadedDate { get; set; } = DateTime.Now;

        public bool IsActive { get; set; } = true;
    }

    [TenantAttribute("Default")]
    [SugarTable("ExamSubmissions")]
    public class ExamSubmission
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        public int ExamPaperId { get; set; }

        [SugarColumn(Length = 100, IsNullable = false)]
        public string StudentName { get; set; } = string.Empty;

        [SugarColumn(Length = 50, IsNullable = false)]
        public string StudentNumber { get; set; } = string.Empty;

        [SugarColumn(Length = 255, IsNullable = false)]
        public string AnswerFileName { get; set; } = string.Empty;

        [SugarColumn(Length = 500, IsNullable = false)]
        public string AnswerFilePath { get; set; } = string.Empty;

        public DateTime SubmittedDate { get; set; } = DateTime.Now;

        [SugarColumn(DecimalDigits = 2, IsNullable = true)]
        public decimal? Score { get; set; }

        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Comments { get; set; }

        [SugarColumn(Length = 100, IsNullable = true)]
        public string? GradedBy { get; set; }
    }
    
    [SugarTable("Nextkeys")]
    public class Nextkeys
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(Length = 10, IsNullable = true)]
        public string? KeyPrefix { get; set; }

        [SugarColumn(Length = 25, IsNullable = true)]
        public string? SubKey { get; set; }

        [SugarColumn(IsNullable = true)]
        public int? KeyID { get; set; }
    }
}
